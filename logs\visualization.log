2025-05-27 12:09:55,245 - INFO - visualization - visualization.py:722 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 206.58%, Capped: 100.00%
2025-05-27 12:09:55,247 - INFO - visualization - visualization.py:722 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1890.31%, Capped: 100.00%
2025-05-27 12:09:55,248 - INFO - visualization - visualization.py:722 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2740.67%, Capped: 100.00%
2025-05-27 12:09:55,248 - INFO - visualization - visualization.py:722 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3916.57%, Capped: 100.00%
2025-05-27 12:12:24,257 - INFO - visualization - visualization.py:722 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 206.58%, Capped: 100.00%
2025-05-27 12:12:24,261 - INFO - visualization - visualization.py:722 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1890.31%, Capped: 100.00%
2025-05-27 12:12:24,263 - INFO - visualization - visualization.py:722 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2740.67%, Capped: 100.00%
2025-05-27 12:12:24,263 - INFO - visualization - visualization.py:722 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3916.57%, Capped: 100.00%
2025-05-27 12:29:49,756 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 12:29:49,759 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:29:49,761 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 12:29:49,761 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 12:29:49,761 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 12:29:49,788 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:31:50,388 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 12:31:50,388 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:31:50,391 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 12:31:50,391 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 12:31:50,395 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 12:31:50,410 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:32:20,121 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 12:32:20,121 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:32:20,123 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 12:32:20,124 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 12:32:20,130 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 12:32:20,154 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:33:52,751 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 12:33:52,751 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:33:52,751 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 12:33:52,751 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 12:33:52,769 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 12:33:52,792 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:35:02,774 - WARNING - visualization - visualization.py:1056 - No date information available for multi-day stacked bar chart
2025-05-27 12:37:25,236 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 12:37:25,236 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:37:25,236 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 12:37:25,236 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 12:37:25,244 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 12:37:25,268 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:38:28,260 - WARNING - visualization - visualization.py:1056 - No date information available for multi-day stacked bar chart
2025-05-27 12:40:31,167 - WARNING - visualization - visualization.py:1056 - No date information available for multi-day stacked bar chart
2025-05-27 12:41:25,905 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 12:41:25,906 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:41:25,906 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 12:41:25,906 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 12:41:25,906 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 12:41:25,924 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 12:45:32,581 - INFO - visualization - visualization.py:722 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 212.13%, Capped: 100.00%
2025-05-27 12:45:32,583 - INFO - visualization - visualization.py:722 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1727.97%, Capped: 100.00%
2025-05-27 12:45:32,583 - INFO - visualization - visualization.py:722 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2418.48%, Capped: 100.00%
2025-05-27 12:45:32,585 - INFO - visualization - visualization.py:722 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3462.66%, Capped: 100.00%
2025-05-27 12:49:33,179 - INFO - visualization - visualization.py:722 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 212.13%, Capped: 100.00%
2025-05-27 12:49:33,180 - INFO - visualization - visualization.py:722 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1727.97%, Capped: 100.00%
2025-05-27 12:49:33,180 - INFO - visualization - visualization.py:722 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2418.48%, Capped: 100.00%
2025-05-27 12:49:33,181 - INFO - visualization - visualization.py:722 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3462.66%, Capped: 100.00%
2025-05-27 13:05:01,447 - INFO - visualization - visualization.py:722 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 212.13%, Capped: 100.00%
2025-05-27 13:05:01,447 - INFO - visualization - visualization.py:722 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1727.97%, Capped: 100.00%
2025-05-27 13:05:01,449 - INFO - visualization - visualization.py:722 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2418.48%, Capped: 100.00%
2025-05-27 13:05:01,450 - INFO - visualization - visualization.py:722 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3462.66%, Capped: 100.00%
2025-05-27 13:07:10,234 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:07:10,234 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:07:10,234 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:07:10,239 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:07:17,681 - ERROR - visualization - visualization.py:1963 - Error creating power cost comparison plot: 'grid_cost'
2025-05-27 13:07:17,812 - ERROR - visualization - visualization.py:2079 - Error creating power savings plot: 'savings'
2025-05-27 13:09:33,070 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:09:33,070 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:09:33,074 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:09:33,074 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:09:52,907 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:09:52,907 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:09:52,909 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:09:52,909 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:10:02,714 - ERROR - visualization - visualization.py:1963 - Error creating power cost comparison plot: 'grid_cost'
2025-05-27 13:10:03,012 - ERROR - visualization - visualization.py:2079 - Error creating power savings plot: 'savings'
2025-05-27 13:12:34,127 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:12:34,127 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:12:34,127 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:12:34,129 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:12:40,160 - ERROR - visualization - visualization.py:1963 - Error creating power cost comparison plot: 'grid_cost'
2025-05-27 13:12:40,298 - ERROR - visualization - visualization.py:2079 - Error creating power savings plot: 'savings'
2025-05-27 13:14:50,379 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:14:50,380 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:14:50,380 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:14:50,380 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:15:29,683 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:15:29,683 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:15:29,685 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:15:29,685 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:20:08,185 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:20:08,185 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:20:08,185 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:20:08,185 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:20:45,166 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:20:45,166 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:20:45,168 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:20:45,168 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:21:21,787 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:21:21,787 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:21:21,789 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:21:21,789 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:21:46,040 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:21:46,040 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:21:46,040 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:21:46,043 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:22:49,225 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:22:49,225 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:22:49,227 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:22:49,227 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:23:27,350 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 13:23:27,350 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 13:23:27,355 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 13:23:27,355 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 13:23:56,015 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 13:23:56,015 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 13:23:56,017 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 13:23:56,018 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 13:23:56,023 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 13:23:56,040 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 13:24:21,117 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 13:24:21,117 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 13:24:21,117 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 13:24:21,117 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 13:24:21,117 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 13:24:21,153 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 14:20:31,378 - INFO - visualization - visualization.py:722 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 206.58%, Capped: 100.00%
2025-05-27 14:20:31,381 - INFO - visualization - visualization.py:722 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1890.31%, Capped: 100.00%
2025-05-27 14:20:31,381 - INFO - visualization - visualization.py:722 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2740.67%, Capped: 100.00%
2025-05-27 14:20:31,383 - INFO - visualization - visualization.py:722 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3916.57%, Capped: 100.00%
2025-05-27 14:22:48,947 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 14:22:48,947 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 14:22:48,948 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 73546.9, 0: 1194226.1}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 14:22:48,948 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 14:25:58,216 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 14:25:58,216 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 14:25:58,216 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 73546.9, 0: 1194226.1}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 14:25:58,216 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 14:26:47,878 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 14:26:47,878 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 14:26:47,879 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 73546.9, 0: 1194226.1}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 14:26:47,879 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 14:28:10,233 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 14:28:10,234 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 14:28:10,236 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 14:28:10,238 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 14:28:10,245 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 14:28:10,262 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 14:28:34,938 - INFO - visualization - visualization.py:1663 - Combined wind and solar data shape: (1, 5)
2025-05-27 14:28:34,940 - INFO - visualization - visualization.py:1664 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 14:28:34,940 - INFO - visualization - visualization.py:1665 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 12351.59}, 'source': {0: 'Solar'}}
2025-05-27 14:28:34,942 - INFO - visualization - visualization.py:1690 - Grouping data by date and source
2025-05-27 14:28:34,943 - INFO - visualization - visualization.py:1697 - Pivoting data
2025-05-27 14:28:34,965 - INFO - visualization - visualization.py:1807 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 14:28:42,013 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 14:28:42,013 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 14:28:42,013 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 73546.9, 0: 1194226.1}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 14:28:42,013 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 14:29:56,426 - INFO - visualization - visualization.py:722 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 232.91%, Capped: 100.00%
2025-05-27 14:29:56,427 - INFO - visualization - visualization.py:722 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1807.49%, Capped: 100.00%
2025-05-27 14:29:56,428 - INFO - visualization - visualization.py:722 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2563.67%, Capped: 100.00%
2025-05-27 14:29:56,428 - INFO - visualization - visualization.py:722 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3664.60%, Capped: 100.00%
2025-05-27 14:34:49,113 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 14:34:49,113 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 14:34:49,114 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 14:34:49,114 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 14:35:11,245 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 14:35:11,247 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 14:35:11,247 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 14:35:11,249 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 14:36:36,384 - INFO - visualization - visualization.py:1554 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 14:36:36,384 - INFO - visualization - visualization.py:1555 - DataFrame shape: (4, 4)
2025-05-27 14:36:36,384 - INFO - visualization - visualization.py:1556 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 14:36:36,390 - INFO - visualization - visualization.py:1569 - No date column found, creating bar chart for aggregated data
2025-05-27 14:37:57,638 - INFO - visualization - visualization.py:1442 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 14:37:57,638 - INFO - visualization - visualization.py:1443 - DataFrame shape: (4, 4)
2025-05-27 14:37:57,639 - INFO - visualization - visualization.py:1444 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 64639.4, 0: 977943.9}, 'consumption_kwh': {2: 30785.856, 0: 71218.56}, 'is_peak': {2: True, 0: True}}
2025-05-27 14:37:57,639 - INFO - visualization - visualization.py:1457 - No date column found, creating bar chart for aggregated data
2025-05-27 14:41:29,091 - INFO - visualization - visualization.py:737 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 213.71%, Capped: 100.00%
2025-05-27 14:41:29,091 - INFO - visualization - visualization.py:737 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1375.58%, Capped: 100.00%
2025-05-27 14:41:29,092 - INFO - visualization - visualization.py:737 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 1752.49%, Capped: 100.00%
2025-05-27 14:41:29,092 - INFO - visualization - visualization.py:737 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 2859.32%, Capped: 100.00%
2025-05-27 14:44:32,000 - INFO - visualization - visualization.py:737 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 213.71%, Capped: 100.00%
2025-05-27 14:44:32,001 - INFO - visualization - visualization.py:737 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1375.58%, Capped: 100.00%
2025-05-27 14:44:32,001 - INFO - visualization - visualization.py:737 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 1752.49%, Capped: 100.00%
2025-05-27 14:44:32,002 - INFO - visualization - visualization.py:737 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 2859.32%, Capped: 100.00%
2025-05-27 14:48:44,321 - INFO - visualization - visualization.py:737 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 213.71%, Capped: 100.00%
2025-05-27 14:48:44,322 - INFO - visualization - visualization.py:737 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1375.58%, Capped: 100.00%
2025-05-27 14:48:44,322 - INFO - visualization - visualization.py:737 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 1752.49%, Capped: 100.00%
2025-05-27 14:48:44,323 - INFO - visualization - visualization.py:737 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 2859.32%, Capped: 100.00%
2025-05-27 14:50:17,046 - INFO - visualization - visualization.py:1457 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-27 14:50:17,046 - INFO - visualization - visualization.py:1458 - DataFrame shape: (4, 4)
2025-05-27 14:50:17,047 - INFO - visualization - visualization.py:1459 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 73815.6, 0: 1124400.1}, 'consumption_kwh': {2: 34634.088, 0: 80120.88}, 'is_peak': {2: True, 0: True}}
2025-05-27 14:50:17,048 - INFO - visualization - visualization.py:1472 - No date column found, creating bar chart for aggregated data
2025-05-27 16:51:04,455 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (2, 5)
2025-05-27 16:51:04,456 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 16:51:04,458 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 12351.59, 1: 63663.0}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-27 16:51:04,460 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-27 16:51:04,464 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-27 16:51:04,490 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 17:38:51,154 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (2, 5)
2025-05-27 17:38:51,154 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 17:38:51,157 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-26 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 12351.59, 1: 63663.0}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-27 17:38:51,159 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-27 17:38:51,162 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-27 17:38:51,176 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 17:40:42,793 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (16, 5)
2025-05-27 17:40:42,793 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-27 17:40:42,793 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-14 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-15 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-14 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-15 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 22986.43, 1: 10064.4}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-05-27 17:40:42,793 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-27 17:40:42,808 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-27 17:40:42,839 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 10:22:40,263 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (1, 5)
2025-05-28 10:22:40,265 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 10:22:40,267 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-27 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-27 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 13917.72}, 'source': {0: 'Solar'}}
2025-05-28 10:22:40,272 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 10:22:40,277 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 10:22:40,302 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 10:23:35,315 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (1, 5)
2025-05-28 10:23:35,315 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 10:23:35,322 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-27 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-27 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 13917.72}, 'source': {0: 'Solar'}}
2025-05-28 10:23:35,324 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 10:23:35,328 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 10:23:35,338 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 10:27:16,932 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (1, 5)
2025-05-28 10:27:16,932 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 10:27:16,937 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-27 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-27 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 13917.72}, 'source': {0: 'Solar'}}
2025-05-28 10:27:16,938 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 10:27:16,941 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 10:27:16,949 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 10:37:35,904 - ERROR - visualization - visualization.py:1992 - Error creating power cost comparison plot: No module named 'src.data'
2025-05-28 10:37:36,831 - ERROR - visualization - visualization.py:2120 - Error creating power savings plot: No module named 'src.data'
2025-05-28 10:37:36,852 - ERROR - visualization - visualization.py:2121 - Traceback (most recent call last):
  File "D:\Harikrishnan\Plot Generation\backend\utils\visualization.py", line 2102, in create_power_savings_plot
    from src.data import get_plant_display_name
ModuleNotFoundError: No module named 'src.data'

2025-05-28 10:50:06,994 - INFO - visualization - visualization.py:1457 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 10:50:06,994 - INFO - visualization - visualization.py:1458 - DataFrame shape: (4, 4)
2025-05-28 10:50:06,994 - INFO - visualization - visualization.py:1459 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 39514.700000000004, 0: 582288.7}, 'consumption_kwh': {2: 19241.16, 0: 44511.6}, 'is_peak': {2: True, 0: True}}
2025-05-28 10:50:06,994 - INFO - visualization - visualization.py:1472 - No date column found, creating bar chart for aggregated data
2025-05-28 11:10:32,825 - INFO - visualization - visualization.py:1457 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 11:10:32,825 - INFO - visualization - visualization.py:1458 - DataFrame shape: (4, 4)
2025-05-28 11:10:32,826 - INFO - visualization - visualization.py:1459 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 56460.5, 0: 884964.7000000001}, 'consumption_kwh': {2: 26937.624, 0: 62316.24}, 'is_peak': {2: True, 0: True}}
2025-05-28 11:10:32,826 - INFO - visualization - visualization.py:1472 - No date column found, creating bar chart for aggregated data
2025-05-28 11:12:51,673 - INFO - visualization - visualization.py:1457 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 11:12:51,675 - INFO - visualization - visualization.py:1458 - DataFrame shape: (4, 4)
2025-05-28 11:12:51,676 - INFO - visualization - visualization.py:1459 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 56460.5, 0: 884964.7000000001}, 'consumption_kwh': {2: 26937.624, 0: 62316.24}, 'is_peak': {2: True, 0: True}}
2025-05-28 11:12:51,676 - INFO - visualization - visualization.py:1472 - No date column found, creating bar chart for aggregated data
2025-05-28 11:15:42,793 - INFO - visualization - visualization.py:1457 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 11:15:42,793 - INFO - visualization - visualization.py:1458 - DataFrame shape: (4, 4)
2025-05-28 11:15:42,796 - INFO - visualization - visualization.py:1459 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 56460.5, 0: 884964.7000000001}, 'consumption_kwh': {2: 26937.624, 0: 62316.24}, 'is_peak': {2: True, 0: True}}
2025-05-28 11:15:42,797 - INFO - visualization - visualization.py:1472 - No date column found, creating bar chart for aggregated data
2025-05-28 11:17:52,923 - INFO - visualization - visualization.py:737 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 280.10%, Capped: 100.00%
2025-05-28 11:17:52,928 - INFO - visualization - visualization.py:737 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 2013.40%, Capped: 100.00%
2025-05-28 11:17:52,928 - INFO - visualization - visualization.py:737 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2813.63%, Capped: 100.00%
2025-05-28 11:17:52,929 - INFO - visualization - visualization.py:737 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 2678.27%, Capped: 100.00%
2025-05-28 11:30:10,676 - INFO - visualization - visualization.py:737 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 280.10%, Capped: 100.00%
2025-05-28 11:30:10,680 - INFO - visualization - visualization.py:737 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 2013.40%, Capped: 100.00%
2025-05-28 11:30:10,680 - INFO - visualization - visualization.py:737 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2813.63%, Capped: 100.00%
2025-05-28 11:30:10,682 - INFO - visualization - visualization.py:737 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 2678.27%, Capped: 100.00%
2025-05-28 13:14:41,986 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (2, 5)
2025-05-28 13:14:41,986 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 13:14:41,993 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-04-17 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-17 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-04-17 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-17 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 12207.54, 1: 12400.97}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-28 13:14:41,994 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 13:14:42,002 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 13:14:42,029 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 13:17:19,508 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (2, 5)
2025-05-28 13:17:19,509 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 13:17:19,511 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 4841.0, 1: 4137.36}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-28 13:17:19,511 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 13:17:19,511 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 13:17:19,527 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 13:17:20,880 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (2, 5)
2025-05-28 13:17:20,880 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 13:17:20,880 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 4841.0, 1: 4137.36}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-28 13:17:20,880 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 13:17:20,893 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 13:17:20,908 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 15:23:19,266 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (2, 5)
2025-05-28 15:23:19,267 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 15:23:19,268 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 7023.13, 1: 8735.7}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-28 15:23:19,269 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 15:23:19,273 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 15:23:19,293 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 16:07:37,274 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (2, 5)
2025-05-28 16:07:37,274 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 16:07:37,279 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 7620.53, 1: 10160.79}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-28 16:07:37,279 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 16:07:37,289 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 16:07:37,316 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 16:08:36,793 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (44, 5)
2025-05-28 16:08:36,793 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 16:08:36,795 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-02 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-02 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 21647.79, 1: 17566.52}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-05-28 16:08:36,797 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 16:08:36,802 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 16:08:36,825 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 16:11:38,522 - INFO - visualization - visualization.py:1678 - Combined wind and solar data shape: (44, 5)
2025-05-28 16:11:38,522 - INFO - visualization - visualization.py:1679 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 16:11:38,522 - INFO - visualization - visualization.py:1680 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-02 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-02 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 21647.79, 1: 17566.52}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-05-28 16:11:38,522 - INFO - visualization - visualization.py:1705 - Grouping data by date and source
2025-05-28 16:11:38,522 - INFO - visualization - visualization.py:1712 - Pivoting data
2025-05-28 16:11:38,542 - INFO - visualization - visualization.py:1822 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 16:34:14,606 - INFO - visualization - visualization.py:1457 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 16:34:14,606 - INFO - visualization - visualization.py:1458 - DataFrame shape: (4, 4)
2025-05-28 16:34:14,608 - INFO - visualization - visualization.py:1459 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 251917.4, 0: 5277119.2}, 'consumption_kwh': {2: 88117.824, 0: 209793.00679999997}, 'is_peak': {2: True, 0: True}}
2025-05-28 16:34:14,608 - INFO - visualization - visualization.py:1472 - No date column found, creating bar chart for aggregated data
2025-05-28 16:59:01,399 - INFO - visualization - visualization.py:1461 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 16:59:01,399 - INFO - visualization - visualization.py:1462 - DataFrame shape: (4, 4)
2025-05-28 16:59:01,399 - INFO - visualization - visualization.py:1463 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 251917.4, 0: 5277119.2}, 'consumption_kwh': {2: 88117.824, 0: 209793.00679999997}, 'is_peak': {2: True, 0: True}}
2025-05-28 16:59:01,399 - INFO - visualization - visualization.py:1476 - No date column found, creating bar chart for aggregated data
2025-05-28 17:00:25,859 - INFO - visualization - visualization.py:1463 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 17:00:25,859 - INFO - visualization - visualization.py:1464 - DataFrame shape: (4, 4)
2025-05-28 17:00:25,862 - INFO - visualization - visualization.py:1465 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 251917.4, 0: 5277119.2}, 'consumption_kwh': {2: 88117.824, 0: 209793.00679999997}, 'is_peak': {2: True, 0: True}}
2025-05-28 17:00:25,862 - INFO - visualization - visualization.py:1478 - No date column found, creating bar chart for aggregated data
2025-05-28 17:03:24,458 - INFO - visualization - visualization.py:1465 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 17:03:24,458 - INFO - visualization - visualization.py:1466 - DataFrame shape: (4, 4)
2025-05-28 17:03:24,460 - INFO - visualization - visualization.py:1467 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 251917.4, 0: 5277119.2}, 'consumption_kwh': {2: 88117.824, 0: 209793.00679999997}, 'is_peak': {2: True, 0: True}}
2025-05-28 17:03:24,460 - INFO - visualization - visualization.py:1480 - No date column found, creating bar chart for aggregated data
2025-05-28 17:04:07,689 - INFO - visualization - visualization.py:1467 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 17:04:07,689 - INFO - visualization - visualization.py:1468 - DataFrame shape: (4, 4)
2025-05-28 17:04:07,690 - INFO - visualization - visualization.py:1469 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 251917.4, 0: 5277119.2}, 'consumption_kwh': {2: 88117.824, 0: 209793.00679999997}, 'is_peak': {2: True, 0: True}}
2025-05-28 17:04:07,690 - INFO - visualization - visualization.py:1482 - No date column found, creating bar chart for aggregated data
2025-05-28 17:06:29,733 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (2, 5)
2025-05-28 17:06:29,744 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 17:06:29,807 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 7903.67, 1: 12086.32}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-28 17:06:29,846 - INFO - visualization - visualization.py:1715 - Grouping data by date and source
2025-05-28 17:06:29,849 - INFO - visualization - visualization.py:1722 - Pivoting data
2025-05-28 17:06:29,872 - INFO - visualization - visualization.py:1832 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-28 17:06:43,497 - INFO - visualization - visualization.py:1467 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-28 17:06:43,498 - INFO - visualization - visualization.py:1468 - DataFrame shape: (4, 4)
2025-05-28 17:06:43,498 - INFO - visualization - visualization.py:1469 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 251917.4, 0: 5277119.2}, 'consumption_kwh': {2: 88117.824, 0: 209793.00679999997}, 'is_peak': {2: True, 0: True}}
2025-05-28 17:06:43,499 - INFO - visualization - visualization.py:1482 - No date column found, creating bar chart for aggregated data
2025-05-29 17:39:38,730 - INFO - visualization - visualization.py:743 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 206.58%, Capped: 100.00%
2025-05-29 17:39:38,730 - INFO - visualization - visualization.py:743 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1890.31%, Capped: 100.00%
2025-05-29 17:39:38,730 - INFO - visualization - visualization.py:743 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2740.67%, Capped: 100.00%
2025-05-29 17:39:38,730 - INFO - visualization - visualization.py:743 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3916.57%, Capped: 100.00%
2025-05-29 17:42:11,259 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (2, 5)
2025-05-29 17:42:11,259 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-29 17:42:11,261 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-29 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-29 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-29 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-29 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 14688.17, 1: 35274.79}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-29 17:42:11,263 - INFO - visualization - visualization.py:1715 - Grouping data by date and source
2025-05-29 17:42:11,268 - INFO - visualization - visualization.py:1722 - Pivoting data
2025-05-29 17:42:11,295 - INFO - visualization - visualization.py:1832 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-29 17:42:17,146 - INFO - visualization - visualization.py:1467 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-29 17:42:17,147 - INFO - visualization - visualization.py:1468 - DataFrame shape: (4, 4)
2025-05-29 17:42:17,148 - INFO - visualization - visualization.py:1469 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 72802.5, 0: 1131773.6}, 'consumption_kwh': {2: 34634.088, 0: 80120.88}, 'is_peak': {2: True, 0: True}}
2025-05-29 17:42:17,148 - INFO - visualization - visualization.py:1482 - No date column found, creating bar chart for aggregated data
2025-05-29 17:49:13,423 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (2, 5)
2025-05-29 17:49:13,426 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-29 17:49:13,428 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 8252.94, 1: 56707.0}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-29 17:49:13,428 - INFO - visualization - visualization.py:1715 - Grouping data by date and source
2025-05-29 17:49:13,430 - INFO - visualization - visualization.py:1722 - Pivoting data
2025-05-29 17:49:13,439 - INFO - visualization - visualization.py:1832 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-29 17:50:27,039 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (2, 5)
2025-05-29 17:50:27,039 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-29 17:50:27,041 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-28 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 8252.94, 1: 56707.0}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-29 17:50:27,044 - INFO - visualization - visualization.py:1715 - Grouping data by date and source
2025-05-29 17:50:27,047 - INFO - visualization - visualization.py:1722 - Pivoting data
2025-05-29 17:50:27,071 - INFO - visualization - visualization.py:1832 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-29 17:50:35,269 - INFO - visualization - visualization.py:1467 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-05-29 17:50:35,269 - INFO - visualization - visualization.py:1468 - DataFrame shape: (4, 4)
2025-05-29 17:50:35,271 - INFO - visualization - visualization.py:1469 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 72802.5, 0: 1131773.6}, 'consumption_kwh': {2: 34634.088, 0: 80120.88}, 'is_peak': {2: True, 0: True}}
2025-05-29 17:50:35,272 - INFO - visualization - visualization.py:1482 - No date column found, creating bar chart for aggregated data
2025-05-29 17:50:53,227 - INFO - visualization - visualization.py:743 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 206.58%, Capped: 100.00%
2025-05-29 17:50:53,235 - INFO - visualization - visualization.py:743 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1890.31%, Capped: 100.00%
2025-05-29 17:50:53,235 - INFO - visualization - visualization.py:743 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2740.67%, Capped: 100.00%
2025-05-29 17:50:53,235 - INFO - visualization - visualization.py:743 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3916.57%, Capped: 100.00%
2025-05-29 17:50:58,189 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (16, 5)
2025-05-29 17:50:58,189 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-29 17:50:58,199 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-22 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-23 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-22 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-23 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 14419.79, 1: 17421.62}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-05-29 17:50:58,205 - INFO - visualization - visualization.py:1715 - Grouping data by date and source
2025-05-29 17:50:58,228 - INFO - visualization - visualization.py:1722 - Pivoting data
2025-05-29 17:50:58,253 - INFO - visualization - visualization.py:1832 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-30 11:55:49,041 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (2, 5)
2025-05-30 11:55:49,103 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-30 11:55:49,109 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-30 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-30 00:00:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-30 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-30 00:00:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANSP'}, 'generation_kwh': {0: 5080.3, 1: 26049.77}, 'source': {0: 'Solar', 1: 'Wind'}}
2025-05-30 11:55:49,109 - INFO - visualization - visualization.py:1715 - Grouping data by date and source
2025-05-30 11:55:49,114 - INFO - visualization - visualization.py:1722 - Pivoting data
2025-05-30 11:55:49,140 - INFO - visualization - visualization.py:1832 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-30 11:57:00,173 - INFO - visualization - visualization.py:743 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 206.58%, Capped: 100.00%
2025-05-30 11:57:00,175 - INFO - visualization - visualization.py:743 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1890.31%, Capped: 100.00%
2025-05-30 11:57:00,178 - INFO - visualization - visualization.py:743 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2740.67%, Capped: 100.00%
2025-05-30 11:57:00,178 - INFO - visualization - visualization.py:743 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3916.57%, Capped: 100.00%
2025-05-30 12:02:06,173 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (96, 5)
2025-05-30 12:02:06,173 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['time', 'date', 'generation_kwh', 'source', 'plant_long_name']
2025-05-30 12:02:06,175 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'time': {0: Timestamp('2024-01-15 00:00:00'), 1: Timestamp('2024-01-15 00:15:00')}, 'date': {0: Timestamp('2024-01-15 00:00:00'), 1: Timestamp('2024-01-15 00:15:00')}, 'generation_kwh': {0: 10.5, 1: 10.5}, 'source': {0: 'Solar', 1: 'Solar'}, 'plant_long_name': {0: 'Test Plant', 1: 'Test Plant'}}
2025-05-30 12:02:06,176 - INFO - visualization - visualization.py:1719 - Using time column for single day 15-minute data
2025-05-30 12:02:06,176 - INFO - visualization - visualization.py:1727 - Grouping single day data by time and source
2025-05-30 12:02:06,179 - INFO - visualization - visualization.py:1731 - Pivoting single day data
2025-05-30 12:02:06,188 - INFO - visualization - visualization.py:1866 - DataFrame columns for plant names: ['time', 'date', 'generation_kwh', 'source', 'plant_long_name']
2025-05-30 12:02:06,211 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (6, 4)
2025-05-30 12:02:06,211 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['date', 'generation_kwh', 'source', 'plant_long_name']
2025-05-30 12:02:06,211 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'date': {0: Timestamp('2024-01-15 00:00:00'), 1: Timestamp('2024-01-16 00:00:00')}, 'generation_kwh': {0: 100.0, 1: 120.0}, 'source': {0: 'Solar', 1: 'Solar'}, 'plant_long_name': {0: 'Test Plant', 1: 'Test Plant'}}
2025-05-30 12:02:06,211 - INFO - visualization - visualization.py:1742 - Grouping data by date and source
2025-05-30 12:02:06,218 - INFO - visualization - visualization.py:1749 - Pivoting data
2025-05-30 12:02:06,227 - INFO - visualization - visualization.py:1866 - DataFrame columns for plant names: ['date', 'generation_kwh', 'source', 'plant_long_name']
2025-05-30 12:03:33,015 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (192, 5)
2025-05-30 12:03:33,016 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-30 12:03:33,018 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-01 00:15:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-04-01 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-04-01 00:15:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.ANS1', 1: 'IN.INTE.ANS1'}, 'generation_kwh': {0: 0.0, 1: 0.0}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-05-30 12:03:33,020 - INFO - visualization - visualization.py:1719 - Using time column for single day 15-minute data
2025-05-30 12:03:33,021 - INFO - visualization - visualization.py:1727 - Grouping single day data by time and source
2025-05-30 12:03:33,028 - INFO - visualization - visualization.py:1731 - Pivoting single day data
2025-05-30 12:03:33,050 - INFO - visualization - visualization.py:1866 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-30 12:03:48,136 - INFO - visualization - visualization.py:743 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 206.58%, Capped: 100.00%
2025-05-30 12:03:48,136 - INFO - visualization - visualization.py:743 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 1890.31%, Capped: 100.00%
2025-05-30 12:03:48,153 - INFO - visualization - visualization.py:743 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2740.67%, Capped: 100.00%
2025-05-30 12:03:48,153 - INFO - visualization - visualization.py:743 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 3916.57%, Capped: 100.00%
2025-05-30 15:22:15,960 - INFO - visualization - visualization.py:1688 - Combined wind and solar data shape: (288, 5)
2025-05-30 15:22:15,961 - INFO - visualization - visualization.py:1689 - Combined wind and solar data columns: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-05-30 15:22:15,965 - INFO - visualization - visualization.py:1690 - Combined wind and solar data sample: {'time': {0: Timestamp('2025-05-30 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-30 00:15:00+0530', tz='UTC+05:30')}, 'date': {0: Timestamp('2025-05-30 00:00:00+0530', tz='UTC+05:30'), 1: Timestamp('2025-05-30 00:15:00+0530', tz='UTC+05:30')}, 'PLANT_LONG_NAME': {0: 'IN.INTE.KSIS', 1: 'IN.INTE.KSIS'}, 'generation_kwh': {0: 0.0, 1: 0.0}, 'source': {0: 'Solar', 1: 'Solar'}}
2025-05-30 15:22:15,967 - INFO - visualization - visualization.py:1719 - Using time column for single day 15-minute data
2025-05-30 15:22:15,967 - INFO - visualization - visualization.py:1727 - Grouping single day data by time and source
2025-05-30 15:22:15,972 - INFO - visualization - visualization.py:1731 - Pivoting single day data
2025-05-30 15:22:15,999 - INFO - visualization - visualization.py:1866 - DataFrame columns for plant names: ['time', 'date', 'PLANT_LONG_NAME', 'generation_kwh', 'source']
2025-06-04 11:23:36,127 - INFO - visualization - visualization.py:743 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 32.55%, Capped: 32.55%
2025-06-04 11:23:36,128 - INFO - visualization - visualization.py:743 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 69.59%, Capped: 69.59%
2025-06-04 11:23:36,128 - INFO - visualization - visualization.py:743 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 0.29%, Capped: 0.29%
2025-06-04 11:23:36,129 - INFO - visualization - visualization.py:743 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 8.01%, Capped: 8.01%
2025-06-04 11:28:59,484 - INFO - visualization - visualization.py:743 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 32.55%, Capped: 32.55%
2025-06-04 11:28:59,484 - INFO - visualization - visualization.py:743 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 69.59%, Capped: 69.59%
2025-06-04 11:28:59,487 - INFO - visualization - visualization.py:743 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 0.29%, Capped: 0.29%
2025-06-04 11:28:59,487 - INFO - visualization - visualization.py:743 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 8.01%, Capped: 8.01%
2025-06-04 11:35:14,040 - INFO - visualization - visualization.py:1467 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-06-04 11:35:14,041 - INFO - visualization - visualization.py:1468 - DataFrame shape: (4, 4)
2025-06-04 11:35:14,041 - INFO - visualization - visualization.py:1469 - DataFrame sample: {'tod_bin': {2: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {2: 39240.0, 0: 139579.5}, 'consumption_kwh': {2: 123143.424, 0: 284874.24}, 'is_peak': {2: True, 0: True}}
2025-06-04 11:35:14,041 - INFO - visualization - visualization.py:1482 - No date column found, creating bar chart for aggregated data
2025-06-04 11:35:14,744 - INFO - visualization - visualization.py:743 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 32.55%, Capped: 32.55%
2025-06-04 11:35:14,744 - INFO - visualization - visualization.py:743 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 69.59%, Capped: 69.59%
2025-06-04 11:35:14,744 - INFO - visualization - visualization.py:743 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 0.29%, Capped: 0.29%
2025-06-04 11:35:14,746 - INFO - visualization - visualization.py:743 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 8.01%, Capped: 8.01%
2025-06-04 11:38:27,689 - INFO - visualization - visualization.py:743 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 35.49%, Capped: 35.49%
2025-06-04 11:38:27,689 - INFO - visualization - visualization.py:743 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 56.14%, Capped: 56.14%
2025-06-04 11:38:27,689 - INFO - visualization - visualization.py:743 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 0.22%, Capped: 0.22%
2025-06-04 11:38:27,689 - INFO - visualization - visualization.py:743 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 6.87%, Capped: 6.87%
2025-06-04 11:48:22,485 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 27109.10 kWh, Max Gen Value: 1617.60 kWh
2025-06-04 11:48:22,486 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 11:48:22,487 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 11:48:50,468 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 27109.10 kWh, Max Gen Value: 1617.60 kWh
2025-06-04 11:48:50,469 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 11:48:50,469 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 11:49:43,116 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 27109.10 kWh, Max Gen Value: 1617.60 kWh
2025-06-04 11:49:43,117 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 11:49:43,117 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 11:50:06,225 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 27109.10 kWh, Max Gen Value: 1617.60 kWh
2025-06-04 11:50:06,226 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 11:50:06,226 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 11:50:49,482 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 27109.10 kWh, Max Gen Value: 1617.60 kWh
2025-06-04 11:50:49,483 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 11:50:49,483 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 11:51:05,705 - INFO - visualization - visualization.py:831 - Summary Generation Only Plot - Total Gen: 27109.10 kWh, Max Gen Value: 1617.60 kWh
2025-06-04 11:51:05,707 - INFO - visualization - visualization.py:832 - Summary Generation Only Plot - Data points: 96
2025-06-04 11:51:30,294 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 27109.10 kWh, Max Gen Value: 19990.70 kWh
2025-06-04 11:51:30,294 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 91654.15 kWh, Max Cons Value: 35609.28 kWh
2025-06-04 11:51:30,294 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 11:51:30,295 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 624.71 kWh per 15-min interval
2025-06-04 11:51:30,295 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 1112.79 kWh per 15-min interval
2025-06-04 11:51:30,310 - INFO - visualization - visualization.py:784 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 35.49%, Capped: 35.49%
2025-06-04 11:51:30,311 - INFO - visualization - visualization.py:784 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 56.14%, Capped: 56.14%
2025-06-04 11:51:30,311 - INFO - visualization - visualization.py:784 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 0.22%, Capped: 0.22%
2025-06-04 11:51:30,312 - INFO - visualization - visualization.py:784 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 6.87%, Capped: 6.87%
2025-06-04 11:51:43,874 - INFO - visualization - visualization.py:922 - ToD Generation Only Plot - Total Gen: 27109.10 kWh, Max Gen Value: 19990.70 kWh
2025-06-04 11:51:43,874 - INFO - visualization - visualization.py:923 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 11:51:43,876 - INFO - visualization - visualization.py:944 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 624.71 kWh per 15-min interval
2025-06-04 11:52:42,906 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 11:52:42,908 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 11:52:42,909 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 11:52:55,941 - INFO - visualization - visualization.py:831 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 11:52:55,943 - INFO - visualization - visualization.py:832 - Summary Generation Only Plot - Data points: 96
2025-06-04 11:53:20,980 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 11:53:20,980 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 91654.15 kWh, Max Cons Value: 35609.28 kWh
2025-06-04 11:53:20,981 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 11:53:20,982 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 11:53:20,983 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 1112.79 kWh per 15-min interval
2025-06-04 11:53:21,009 - INFO - visualization - visualization.py:784 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 32.55%, Capped: 32.55%
2025-06-04 11:53:21,011 - INFO - visualization - visualization.py:784 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 69.59%, Capped: 69.59%
2025-06-04 11:53:21,011 - INFO - visualization - visualization.py:784 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 0.29%, Capped: 0.29%
2025-06-04 11:53:21,012 - INFO - visualization - visualization.py:784 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 8.01%, Capped: 8.01%
2025-06-04 11:53:36,836 - INFO - visualization - visualization.py:922 - ToD Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 11:53:36,837 - INFO - visualization - visualization.py:923 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 11:53:36,837 - INFO - visualization - visualization.py:944 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 11:57:23,713 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 11:57:23,713 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 11:57:23,713 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 11:57:34,967 - INFO - visualization - visualization.py:831 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 11:57:34,969 - INFO - visualization - visualization.py:832 - Summary Generation Only Plot - Data points: 96
2025-06-04 11:58:07,846 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 11:58:07,846 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 11:58:07,846 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 11:58:07,847 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 11:58:07,848 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 11:58:07,865 - INFO - visualization - visualization.py:784 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 130.20%, Capped: 100.00%
2025-06-04 11:58:07,866 - INFO - visualization - visualization.py:784 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 278.36%, Capped: 100.00%
2025-06-04 11:58:07,867 - INFO - visualization - visualization.py:784 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 1.14%, Capped: 1.14%
2025-06-04 11:58:07,867 - INFO - visualization - visualization.py:784 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 32.05%, Capped: 32.05%
2025-06-04 11:58:24,033 - INFO - visualization - visualization.py:922 - ToD Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 11:58:24,034 - INFO - visualization - visualization.py:923 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 11:58:24,034 - INFO - visualization - visualization.py:944 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 11:58:54,708 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 11:58:54,708 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 11:58:54,708 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 11:59:06,442 - INFO - visualization - visualization.py:831 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 11:59:06,442 - INFO - visualization - visualization.py:832 - Summary Generation Only Plot - Data points: 96
2025-06-04 11:59:48,622 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 11:59:48,622 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 11:59:48,623 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 11:59:48,624 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 11:59:48,625 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 11:59:48,656 - INFO - visualization - visualization.py:784 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 130.20%, Capped: 100.00%
2025-06-04 11:59:48,657 - INFO - visualization - visualization.py:784 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 278.36%, Capped: 100.00%
2025-06-04 11:59:48,658 - INFO - visualization - visualization.py:784 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 1.14%, Capped: 1.14%
2025-06-04 11:59:48,660 - INFO - visualization - visualization.py:784 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 32.05%, Capped: 32.05%
2025-06-04 12:00:18,087 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 28241.50 kWh, Max Gen Value: 2293.20 kWh
2025-06-04 12:00:18,088 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:00:18,088 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:00:20,548 - INFO - visualization - visualization.py:922 - ToD Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 12:00:20,548 - INFO - visualization - visualization.py:923 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:00:20,551 - INFO - visualization - visualization.py:944 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 12:00:37,653 - INFO - visualization - visualization.py:831 - Summary Generation Only Plot - Total Gen: 28241.50 kWh, Max Gen Value: 2293.20 kWh
2025-06-04 12:00:37,654 - INFO - visualization - visualization.py:832 - Summary Generation Only Plot - Data points: 96
2025-06-04 12:01:11,206 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 28241.50 kWh, Max Gen Value: 21904.90 kWh
2025-06-04 12:01:11,207 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:01:11,207 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:01:11,209 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 684.53 kWh per 15-min interval
2025-06-04 12:01:11,209 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:01:11,239 - INFO - visualization - visualization.py:784 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 128.97%, Capped: 100.00%
2025-06-04 12:01:11,240 - INFO - visualization - visualization.py:784 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 246.06%, Capped: 100.00%
2025-06-04 12:01:11,240 - INFO - visualization - visualization.py:784 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2.23%, Capped: 2.23%
2025-06-04 12:01:11,241 - INFO - visualization - visualization.py:784 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 21.71%, Capped: 21.71%
2025-06-04 12:01:27,250 - INFO - visualization - visualization.py:922 - ToD Generation Only Plot - Total Gen: 28241.50 kWh, Max Gen Value: 21904.90 kWh
2025-06-04 12:01:27,250 - INFO - visualization - visualization.py:923 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:01:27,251 - INFO - visualization - visualization.py:944 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 684.53 kWh per 15-min interval
2025-06-04 12:02:28,377 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:02:28,377 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:02:28,377 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:02:40,734 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:02:40,734 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 12:02:59,458 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:02:59,458 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:02:59,459 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:03:15,978 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:03:15,980 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:03:15,980 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:03:34,352 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:03:34,352 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:03:34,353 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:03:53,669 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:03:53,669 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:03:53,670 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:04:05,650 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:04:05,650 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 12:04:42,692 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 12:04:42,692 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:04:42,692 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:04:42,693 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 12:04:42,693 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:04:42,709 - INFO - visualization - visualization.py:791 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 130.20%, Capped: 100.00%
2025-06-04 12:04:42,710 - INFO - visualization - visualization.py:791 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 278.36%, Capped: 100.00%
2025-06-04 12:04:42,710 - INFO - visualization - visualization.py:791 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 1.14%, Capped: 1.14%
2025-06-04 12:04:42,711 - INFO - visualization - visualization.py:791 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 32.05%, Capped: 32.05%
2025-06-04 12:04:56,635 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 12:04:56,635 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:04:56,635 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 12:06:19,335 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:06:19,337 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:06:19,337 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:06:39,795 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:06:39,795 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:06:39,796 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:06:52,664 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:06:52,664 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:06:52,665 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:07:05,632 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:07:05,633 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 12:07:42,987 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:07:42,987 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:07:42,988 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:07:49,743 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 168079.00 kWh, Max Gen Value: 29768.00 kWh
2025-06-04 12:07:49,745 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 8
2025-06-04 12:07:53,704 - INFO - visualization - visualization.py:1591 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-06-04 12:07:53,704 - INFO - visualization - visualization.py:1592 - DataFrame shape: (3, 4)
2025-06-04 12:07:53,706 - INFO - visualization - visualization.py:1593 - DataFrame sample: {'tod_bin': {1: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {1: 42019.75, 0: 100847.4}, 'consumption_kwh': {1: 45827.0752, 0: 109984.98048}, 'is_peak': {1: True, 0: True}}
2025-06-04 12:07:53,706 - INFO - visualization - visualization.py:1606 - No date column found, creating bar chart for aggregated data
2025-06-04 12:08:00,322 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:08:00,323 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 12:08:34,866 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 12:08:34,866 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:08:34,866 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:08:34,867 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 12:08:34,867 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:08:34,886 - INFO - visualization - visualization.py:791 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 130.20%, Capped: 100.00%
2025-06-04 12:08:34,887 - INFO - visualization - visualization.py:791 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 278.36%, Capped: 100.00%
2025-06-04 12:08:34,888 - INFO - visualization - visualization.py:791 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 1.14%, Capped: 1.14%
2025-06-04 12:08:34,889 - INFO - visualization - visualization.py:791 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 32.05%, Capped: 32.05%
2025-06-04 12:08:52,184 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 12:08:52,185 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:08:52,186 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 12:09:09,617 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:09:09,618 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:09:09,620 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:09:45,341 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 235386.90 kWh, Max Gen Value: 48550.10 kWh
2025-06-04 12:09:45,343 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 32, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:09:45,346 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 1517.19 kWh per 15-min interval
2025-06-04 12:11:29,784 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 183308.30 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:11:29,784 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 32, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:11:29,787 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:17:39,246 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 725176.00 kWh, Max Gen Value: 30647.00 kWh
2025-06-04 12:17:39,248 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 30
2025-06-04 12:17:43,827 - INFO - visualization - visualization.py:1591 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-06-04 12:17:43,828 - INFO - visualization - visualization.py:1592 - DataFrame shape: (3, 4)
2025-06-04 12:17:43,829 - INFO - visualization - visualization.py:1593 - DataFrame sample: {'tod_bin': {1: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {1: 181294.0, 0: 435105.6}, 'consumption_kwh': {1: 171851.532, 0: 412443.6768}, 'is_peak': {1: True, 0: True}}
2025-06-04 12:17:43,829 - INFO - visualization - visualization.py:1606 - No date column found, creating bar chart for aggregated data
2025-06-04 12:23:32,875 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 988502.80 kWh, Max Gen Value: 54836.00 kWh
2025-06-04 12:23:32,875 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 120, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:23:32,881 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 1917.97 kWh per 15-min interval
2025-06-04 12:29:17,526 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 687406.13 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:29:17,527 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 120, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:29:17,539 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:41:02,184 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:41:02,185 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:41:02,187 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:41:26,820 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:41:26,821 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 12:41:59,217 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 12:41:59,217 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:41:59,219 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:41:59,221 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 12:41:59,221 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:41:59,280 - INFO - visualization - visualization.py:791 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 130.20%, Capped: 100.00%
2025-06-04 12:41:59,283 - INFO - visualization - visualization.py:791 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 278.36%, Capped: 100.00%
2025-06-04 12:41:59,284 - INFO - visualization - visualization.py:791 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 1.14%, Capped: 1.14%
2025-06-04 12:41:59,284 - INFO - visualization - visualization.py:791 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 32.05%, Capped: 32.05%
2025-06-04 12:42:17,532 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 12:42:17,532 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:42:17,534 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 12:42:32,770 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:42:32,772 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:42:32,774 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:44:01,719 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 725176.00 kWh, Max Gen Value: 30647.00 kWh
2025-06-04 12:44:01,720 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 30
2025-06-04 12:44:06,769 - INFO - visualization - visualization.py:1591 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-06-04 12:44:06,770 - INFO - visualization - visualization.py:1592 - DataFrame shape: (3, 4)
2025-06-04 12:44:06,772 - INFO - visualization - visualization.py:1593 - DataFrame sample: {'tod_bin': {1: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {1: 181294.0, 0: 435105.6}, 'consumption_kwh': {1: 171851.532, 0: 412443.6768}, 'is_peak': {1: True, 0: True}}
2025-06-04 12:44:06,772 - INFO - visualization - visualization.py:1606 - No date column found, creating bar chart for aggregated data
2025-06-04 12:46:04,109 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:46:04,111 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 12:46:04,111 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 12:46:39,724 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 2062.90 kWh
2025-06-04 12:46:39,725 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 12:47:21,361 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 12:47:21,362 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:47:21,362 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:47:21,365 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 12:47:21,365 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:47:21,404 - INFO - visualization - visualization.py:791 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 130.20%, Capped: 100.00%
2025-06-04 12:47:21,405 - INFO - visualization - visualization.py:791 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 278.36%, Capped: 100.00%
2025-06-04 12:47:21,405 - INFO - visualization - visualization.py:791 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 1.14%, Capped: 1.14%
2025-06-04 12:47:21,406 - INFO - visualization - visualization.py:791 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 32.05%, Capped: 32.05%
2025-06-04 12:47:50,375 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 31726.50 kWh, Max Gen Value: 24780.20 kWh
2025-06-04 12:47:50,375 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:47:50,377 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 774.38 kWh per 15-min interval
2025-06-04 12:48:13,193 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:48:13,194 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:48:13,195 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 12:49:55,830 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 26677.00 kWh, Max Gen Value: 26677.00 kWh
2025-06-04 12:49:55,830 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 13575.45 kWh, Max Cons Value: 5274.30 kWh
2025-06-04 12:49:55,830 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:49:55,834 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 833.66 kWh per 15-min interval
2025-06-04 12:49:55,834 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 164.82 kWh per 15-min interval
2025-06-04 12:49:55,887 - INFO - visualization - visualization.py:791 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 0.00%, Capped: 0.00%
2025-06-04 12:49:55,889 - INFO - visualization - visualization.py:791 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 0.00%, Capped: 0.00%
2025-06-04 12:49:55,890 - INFO - visualization - visualization.py:791 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 0.00%, Capped: 0.00%
2025-06-04 12:49:55,891 - INFO - visualization - visualization.py:791 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 764.77%, Capped: 100.00%
2025-06-04 12:49:55,894 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 26677.00 kWh, Max Gen Value: 26677.00 kWh
2025-06-04 12:49:55,894 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:49:55,895 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 833.66 kWh per 15-min interval
2025-06-04 12:49:55,930 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 13575.45 kWh, Max Cons Value: 5274.30 kWh
2025-06-04 12:49:55,931 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:49:55,933 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 164.82 kWh per 15-min interval
2025-06-04 12:49:56,830 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 78085.00 kWh, Max Gen Value: 46851.00 kWh
2025-06-04 12:49:56,831 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 40726.36 kWh, Max Cons Value: 24435.82 kWh
2025-06-04 12:49:56,831 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 3, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)']
2025-06-04 12:49:56,831 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 1464.09 kWh per 15-min interval
2025-06-04 12:49:56,833 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 763.62 kWh per 15-min interval
2025-06-04 12:49:56,870 - INFO - visualization - visualization.py:791 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 191.73%, Capped: 100.00%
2025-06-04 12:49:56,871 - INFO - visualization - visualization.py:791 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 191.73%, Capped: 100.00%
2025-06-04 12:49:56,871 - INFO - visualization - visualization.py:791 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 191.73%, Capped: 100.00%
2025-06-04 12:49:56,872 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 78085.00 kWh, Max Gen Value: 46851.00 kWh
2025-06-04 12:49:56,873 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 3, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)']
2025-06-04 12:49:56,875 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 1464.09 kWh per 15-min interval
2025-06-04 12:49:56,883 - WARNING - visualization - visualization.py:1173 - No date information available for multi-day stacked bar chart
2025-06-04 12:49:56,896 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 40726.36 kWh, Max Cons Value: 24435.82 kWh
2025-06-04 12:49:56,896 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 3, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)']
2025-06-04 12:49:56,897 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 763.62 kWh per 15-min interval
2025-06-04 12:49:56,911 - WARNING - visualization - visualization.py:1495 - No date information available for multi-day stacked bar chart
2025-06-04 12:52:55,650 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 988502.80 kWh, Max Gen Value: 54836.00 kWh
2025-06-04 12:52:55,651 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 120, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:52:55,668 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 1917.97 kWh per 15-min interval
2025-06-04 12:58:33,166 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 687406.13 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 12:58:33,167 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 120, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 12:58:33,185 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 16:24:54,585 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 725176.00 kWh, Max Gen Value: 30647.00 kWh
2025-06-04 16:24:54,644 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 30
2025-06-04 16:25:02,589 - INFO - visualization - visualization.py:1591 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-06-04 16:25:02,589 - INFO - visualization - visualization.py:1592 - DataFrame shape: (3, 4)
2025-06-04 16:25:02,594 - INFO - visualization - visualization.py:1593 - DataFrame sample: {'tod_bin': {1: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {1: 181294.0, 0: 435105.6}, 'consumption_kwh': {1: 171851.532, 0: 412443.6768}, 'is_peak': {1: True, 0: True}}
2025-06-04 16:25:02,594 - INFO - visualization - visualization.py:1606 - No date column found, creating bar chart for aggregated data
2025-06-04 16:31:45,643 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 988502.80 kWh, Max Gen Value: 54836.00 kWh
2025-06-04 16:31:45,643 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 120, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 16:31:45,658 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 1917.97 kWh per 15-min interval
2025-06-04 16:33:11,259 - INFO - visualization - visualization.py:337 - Summary Generation vs Consumption Plot - Total Gen: 28241.50 kWh, Max Gen Value: 2293.20 kWh
2025-06-04 16:33:11,260 - INFO - visualization - visualization.py:338 - Summary Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 278.20 kWh
2025-06-04 16:33:11,260 - INFO - visualization - visualization.py:339 - Summary Generation vs Consumption Plot - Data points: 96, Time column: time_interval
2025-06-04 16:33:34,465 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 28241.50 kWh, Max Gen Value: 2293.20 kWh
2025-06-04 16:33:34,465 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 96
2025-06-04 16:34:20,801 - INFO - visualization - visualization.py:648 - ToD Generation vs Consumption Plot - Total Gen: 28241.50 kWh, Max Gen Value: 21904.90 kWh
2025-06-04 16:34:20,801 - INFO - visualization - visualization.py:649 - ToD Generation vs Consumption Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 16:34:20,801 - INFO - visualization - visualization.py:650 - ToD Generation vs Consumption Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 16:34:20,801 - INFO - visualization - visualization.py:674 - ToD Plot NORMALIZED - Max Gen Value: 684.53 kWh per 15-min interval
2025-06-04 16:34:20,801 - INFO - visualization - visualization.py:675 - ToD Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 16:34:20,873 - INFO - visualization - visualization.py:791 - ToD bin 6 AM - 10 AM (Peak) - Raw replacement %: 128.97%, Capped: 100.00%
2025-06-04 16:34:20,873 - INFO - visualization - visualization.py:791 - ToD bin 10 AM - 6 PM (Off-Peak) - Raw replacement %: 246.06%, Capped: 100.00%
2025-06-04 16:34:20,873 - INFO - visualization - visualization.py:791 - ToD bin 6 PM - 10 PM (Peak) - Raw replacement %: 2.23%, Capped: 2.23%
2025-06-04 16:34:20,877 - INFO - visualization - visualization.py:791 - ToD bin 10 PM - 6 AM (Off-Peak) - Raw replacement %: 21.71%, Capped: 21.71%
2025-06-04 16:34:40,288 - INFO - visualization - visualization.py:935 - ToD Generation Only Plot - Total Gen: 28241.50 kWh, Max Gen Value: 21904.90 kWh
2025-06-04 16:34:40,288 - INFO - visualization - visualization.py:936 - ToD Generation Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 16:34:40,288 - INFO - visualization - visualization.py:957 - ToD Generation Only Plot NORMALIZED - Max Gen Value: 684.53 kWh per 15-min interval
2025-06-04 16:34:55,746 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 22913.54 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 16:34:55,746 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 4, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 16:34:55,746 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
2025-06-04 16:40:51,377 - INFO - visualization - visualization.py:844 - Summary Generation Only Plot - Total Gen: 725176.00 kWh, Max Gen Value: 30647.00 kWh
2025-06-04 16:40:51,377 - INFO - visualization - visualization.py:845 - Summary Generation Only Plot - Data points: 30
2025-06-04 16:40:56,493 - INFO - visualization - visualization.py:1591 - DataFrame columns: ['tod_bin', 'generation_kwh', 'consumption_kwh', 'is_peak']
2025-06-04 16:40:56,494 - INFO - visualization - visualization.py:1592 - DataFrame shape: (3, 4)
2025-06-04 16:40:56,495 - INFO - visualization - visualization.py:1593 - DataFrame sample: {'tod_bin': {1: '6 AM - 10 AM (Peak)', 0: '10 AM - 6 PM (Off-Peak)'}, 'generation_kwh': {1: 181294.0, 0: 435105.6}, 'consumption_kwh': {1: 171851.532, 0: 412443.6768}, 'is_peak': {1: True, 0: True}}
2025-06-04 16:40:56,495 - INFO - visualization - visualization.py:1606 - No date column found, creating bar chart for aggregated data
2025-06-04 16:41:20,020 - INFO - visualization - visualization.py:1261 - ToD Consumption Only Plot - Total Cons: 687406.13 kWh, Max Cons Value: 8902.32 kWh
2025-06-04 16:41:20,020 - INFO - visualization - visualization.py:1262 - ToD Consumption Only Plot - Data points: 120, ToD bins: ['6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)', '6 AM - 10 AM (Peak)', '10 AM - 6 PM (Off-Peak)', '6 PM - 10 PM (Peak)', '10 PM - 6 AM (Off-Peak)']
2025-06-04 16:41:20,020 - INFO - visualization - visualization.py:1283 - ToD Consumption Only Plot NORMALIZED - Max Cons Value: 278.20 kWh per 15-min interval
